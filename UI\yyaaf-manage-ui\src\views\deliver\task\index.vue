<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="地区">
          <x-select show-default v-model="queryParams.areaId" url="/deliverArea/options"></x-select>
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row ref="toolbar" class="table-toolbar">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          v-permission="['deliverTask:add']"
          >新增</el-button
        >
        <el-button
          size="mini"
          type="warning"
          icon="el-icon-finished"
          @click="handleFinishTask"
          v-permission="['deliverTask:finish']"
          >手动结单</el-button
        >
      </el-row>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" align="center" width="50"></el-table-column>
        <el-table-column type="index" label="序号" align="center" min-width="50"></el-table-column>
        <el-table-column prop="name" label="任务名称/地区" align="center" min-width="200">
          <template v-slot="{ row }">
            <task-info :taskId="row.id" :taskName="row.name" :areaName="row.areaName"></task-info>
          </template>
        </el-table-column>
        <el-table-column prop="platform" label="平台" align="center" min-width="130">
          <template v-slot="{ row }">
            <div v-for="item in platformOptions" :key="item.value">
              <el-tag v-if="row.platform == item.value"> {{ item.label }}</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="单价" align="center" min-width="100">
          <template v-slot="{ row }">
            <span>{{ row.price }} 元</span>
          </template>
        </el-table-column>
        <el-table-column prop="target" label="目标/进线/重复" align="center" min-width="150">
          <template v-slot="{ row }">
            {{ row.target }} / {{ row.trueNewFans }} / {{ row.trueRepeatFans }}
          </template>
        </el-table-column>
        <el-table-column
          prop="channelCount"
          label="渠道数"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="accountCount"
          label="主号数"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column label="上次完成/上次结单" align="center" min-width="200">
          <template v-slot="{ row }">
            <span v-if="row.lastFinishTime">{{ row.lastFinishTime }}</span>
            <span v-else>未完成</span>
            <br />
            <span>{{ row.lastStatementTime }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="createOn"
          label="创建时间"
          align="center"
          min-width="200"
        ></el-table-column>
        <el-table-column prop="enable" label="是否启用" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-switch
              v-model="row.enable"
              :active-value="true"
              :inactive-value="false"
              active-color="#13ce66"
              @change="onChangeEnable(row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="200" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              @click="handleAccounts(scope.row)"
              v-permission="['deliverTask:edit']"
              >管理</el-button
            >
            <el-button
              size="mini"
              type="primary"
              @click="handleCopy(scope.row)"
              v-permission="['deliverTask:copy']"
              >复制</el-button
            >
            <br />
            <el-button
              size="mini"
              style="margin-top: 5px"
              type="warning"
              @click="handleEdit(scope.row)"
              v-permission="['deliverTask:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
              v-permission="['deliverTask:delete']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </x-table>
    </el-card>

    <!--<edit-dialog ref="editDialog" @ok="handleOk"/>-->
  </div>
</template>

<script>
import TaskInfo from '@/components/Table/CustomColumn/TaskInfoColumn'
import { tableHeightMixin } from '@/mixin'
import { deliverTaskApi } from '@/api'
import { getDeliverTaskPlatform } from '@/utils/options'
//import EditDialog from './Edit'
export default {
  components: {
    //EditDialog
    TaskInfo,
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      // 选中数组
      taskIds: [],
      taskNames: [],
      queryParams: {},
      loading: false,
      tableData: {},
      platformOptions: [],
    }
  },
  async created() {
    this.platformOptions = await getDeliverTaskPlatform()
  },
  methods: {
    queryReset() {
      this.queryParams = {}
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      if (this.queryParams.date) {
        params.beginTime = this.queryParams.date[0]
        params.endTime = this.queryParams.date[1]
      }
      const res = await deliverTaskApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    handleAdd() {
      //this.$refs.editDialog.add()
      this.$router.push({ path: '/deliver/delivertaskadd' })
    },
    handleEdit(row) {
      this.$router.push({ path: '/deliver/delivertaskadd', query: { id: row.id, operation: 1 } })
    },
    handleCopy(row) {
      this.$router.push({ path: '/deliver/delivertaskadd', query: { id: row.id, operation: 9 } })
    },
    handleAccounts(row) {
      this.$router.push({
        path: '/deliver/deliverchannelaccount',
        query: { taskid: row.id, taskTitle: row.name },
      })
    },
    async handleDelete(row) {
      this.$confirm('是否确认删除该数据项？', '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await deliverTaskApi.delDeliverTask(row.id)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('删除成功')
          } else {
            this.$xMsgError('删除失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },
    onChangeEnable(row) {
      let statusMsg = row.enable == true ? '启用' : '禁用'
      this.$confirm(`是否确认【${statusMsg}】任务状态？`, '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await deliverTaskApi.changeEnable(row.id, row.enable)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$xMsgSuccess('操作成功')
          } else {
            this.$xMsgError('操作失败！' + res.msg)
          }
          this.$refs.table.refresh()
        })
        .catch(() => {
          this.$xloading.hide()
          this.$refs.table.refresh()
        })
    },
    handleOk() {
      this.$refs.table.refresh()
    },
    handleSelectionChange(selection) {
      this.taskIds = selection.map((item) => item.id)
      this.taskNames = selection.map((item) => item.name)
    },
    async handleFinishTask() {
      if (this.taskIds.length > 0) {
        this.$confirm(`是否确认对这批任务进行结单？`, '提示', {
          type: 'warning',
        })
          .then(async () => {
            this.$xloading.show()
            for (let i = 0; i < this.taskIds.length; i++) {
              const res = await deliverTaskApi.finishTask(this.taskIds[i], false)
              this.$xloading.hide()
              if (res.code == 0) {
                this.$xMsgSuccess(`任务【${this.taskNames[i]}】操作成功`)
              } else {
                this.$xMsgError(`任务【${this.taskNames[i]}】操作失败！` + res.msg)
                await this.handleFroceFinishTask(this.taskIds[i], this.taskNames[i], res.msg)
              }
            }
            this.$refs.table.refresh()
          })
          .catch(() => {
            this.$xloading.hide()
            this.$refs.table.refresh()
          })
      } else {
        this.$xMsgWarning('请选择要手动结单的任务！')
      }
    },
    async handleFroceFinishTask(taskId, taskName, errorMsg) {
      this.$confirm(`任务【${taskName}】结单错误：${errorMsg}, 是否确认进行强制结单？`, '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await deliverTaskApi.finishTask(taskId, true)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$xMsgSuccess(`任务【${taskId}】操作成功`)
          } else {
            this.$xMsgError(`任务【${taskId}】操作失败！` + res.msg)
          }
          this.$refs.table.refresh()
        })
        .catch(() => {
          this.$xloading.hide()
          this.$refs.table.refresh()
        })
    },
  },
}
</script>
