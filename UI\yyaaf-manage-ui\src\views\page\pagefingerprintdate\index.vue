<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="日期">
          <el-date-picker
            v-model="queryParams.date"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="类型">
          <x-select v-model="queryParams.type" :options="ipTypeOptions"></x-select>
        </el-form-item>
        <el-form-item label="状态">
          <x-select v-model="queryParams.status" :options="statsStatuOptions"></x-select>
        </el-form-item>
        <el-form-item label="指纹">
          <el-input v-model="queryParams.fingerprintId"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column type="index" label="序号" align="center" min-width="50"></el-table-column>
        <el-table-column prop="date" label="日期" align="center" min-width="100">
          <template v-slot="{ row }">
            <span>{{ row.date.slice(0, 10) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="fingerprintId" label="指纹" align="center" min-width="250">
          <template v-slot="{ row }">
            <span>{{ row.fingerprintId }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag v-if="row.type == 1" type="info">访问</el-tag>
            <el-tag v-if="row.type == 2" type="primary">点击</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="platform" label="平台" align="center" min-width="100">
          <template v-slot="{ row }">
            <div v-for="item in platformOptions" :key="item.value">
              <el-tag v-if="row.platform == item.value"> {{ item.label }}</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="ip" label="IP数" align="center" min-width="100"></el-table-column>
        <el-table-column prop="uv" label="UV数" align="center" min-width="100"></el-table-column>
        <el-table-column prop="pv" label="PV数" align="center" min-width="100"></el-table-column>
        <el-table-column prop="status" label="状态" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag v-if="row.status == 0" type="success">正常</el-tag>
            <el-tag v-else-if="row.status == 1" type="danger">异常</el-tag>
            <el-tag v-else type="danger">黑名单</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="fingerprintStatus" label="指纹状态" align="center" min-width="100">
          <template v-slot="{ row }">
            <div v-for="status in statusOptions" :key="status.value">
              <el-tag
                v-if="row.fingerprintStatus == status.value && row.fingerprintStatus > 0"
                type="warning"
              >
                {{ status.label }}</el-tag
              >
              <el-tag
                v-else-if="row.fingerprintStatus == status.value && row.fingerprintStatus == 0"
                type="success"
              >
                {{ status.label }}</el-tag
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160" align="center">
          <template v-slot="{ row }">
            <el-button
              v-if="row.fingerprintStatus == 0"
              size="mini"
              type="primary"
              @click="handleAddToBlackList(row)"
              v-permission="['pageipblack:add']"
              >添加黑名单</el-button
            >
            <el-button
              v-else
              size="mini"
              type="warning"
              @click="handleRemoveFromBlackList(row)"
              v-permission="['pageipblack:delete']"
              >移除黑名单</el-button
            >
          </template>
        </el-table-column>
      </x-table>
    </el-card>

    <edit-dialog ref="editDialog" @ok="handleOk" />
  </div>
</template>

<script>
import { tableHeightMixin } from '@/mixin'
import { pageFingerprintDateApi, pageFingerprintBlackListApi } from '@/api'
import {
  ipTypeOptions,
  platformOptions,
  getPageCollectRecordStatusOptions,
  statsStatuOptions,
} from '@/utils/options'
import { getLocalISODateString } from '@/utils/index'
import EditDialog from '../pagefingerprintblacklist/edit'

export default {
  components: { EditDialog },
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {
        date: getLocalISODateString(),
        type: -1,
        status: -1,
      },
      loading: false,
      tableData: {},
      ipTypeOptions,
      platformOptions,
      statusOptions: [],
      statsStatuOptions,
    }
  },
  async created() {
    this.statusOptions = await getPageCollectRecordStatusOptions()
  },
  methods: {
    queryReset() {
      this.queryParams = {
        date: getLocalISODateString(),
        type: -1,
        status: -1,
      }
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await pageFingerprintDateApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    async handleAddToBlackList(row) {
      this.$refs.editDialog.add(row.fingerprintId)
    },
    async handleRemoveFromBlackList(row) {
      this.$confirm(`是否确认从指纹黑名单中移除【${row.fingerprintId}】？`, '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await pageFingerprintBlackListApi.delPageFingerprintBlackList(
            row.fingerprintId
          )
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('操作成功')
          } else {
            this.$xMsgError('操作失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },
    handleOk() {
      this.$refs.table.refresh()
    },
  },
}
</script>
