import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/deliverTaskPlatformConfig/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/deliverTaskPlatformConfig/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addDeliverTaskPlatformConfig(data) {
  return request({
    url: '/deliverTaskPlatformConfig/add',
    method: 'post',
    data: data,
  })
}

export function editDeliverTaskPlatformConfig(data) {
  return request({
    url: '/deliverTaskPlatformConfig/edit',
    method: 'post',
    data: data,
  })
}

export function delDeliverTaskPlatformConfig(id) {
  return request({
    url: '/deliverTaskPlatformConfig/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

//----------DeliverTaskPlatformConfig结束----------
