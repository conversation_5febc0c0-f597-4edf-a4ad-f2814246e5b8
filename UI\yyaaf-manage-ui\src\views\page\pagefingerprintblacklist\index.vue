<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="指纹ID">
          <el-input v-model="queryParams.fingerprintId"></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <x-select show-default v-model="queryParams.status" :options="statusOptions"></x-select>
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row ref="toolbar" class="table-toolbar">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          v-permission="['pageFidBlackList:add']"
          >新增</el-button
        >
      </el-row>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="fingerprintId"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column type="index" label="序号" align="center" min-width="50"></el-table-column>
        <el-table-column
          prop="fingerprintId"
          label="指纹ID"
          align="center"
          min-width="200"
        ></el-table-column>
        <el-table-column prop="type" label="禁用状态" align="center" min-width="150">
          <template v-slot="{ row }">
            <div v-for="status in statusOptions" :key="status.value">
              <el-tag v-if="row.status == status.value && row.status > 0" type="warning">
                {{ status.label }}</el-tag
              >
              <el-tag v-else-if="row.status == status.value && row.status == 0" type="success">
                {{ status.label }}</el-tag
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="createOn"
          label="创建时间"
          align="center"
          min-width="100"
        ></el-table-column>

        <el-table-column label="操作" width="160" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="warning"
              @click="handleEdit(scope.row)"
              v-permission="['pageFidBlackList:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
              v-permission="['pageFidBlackList:delete']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </x-table>
    </el-card>

    <edit-dialog ref="editDialog" @ok="handleOk" />
  </div>
</template>

<script>
import { tableHeightMixin } from '@/mixin'
import { pageFingerprintBlackListApi } from '@/api'
import { getPageCollectRecordStatusOptions } from '@/utils/options'
import EditDialog from './edit'
export default {
  components: {
    EditDialog,
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {
        status: -1,
      },
      loading: false,
      tableData: {},
      statusOptions: [],
    }
  },
  async created() {
    this.statusOptions = await getPageCollectRecordStatusOptions()
  },
  methods: {
    queryReset() {
      this.queryParams = {
        status: -1,
      }
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await pageFingerprintBlackListApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    handleAdd() {
      this.$refs.editDialog.add()
    },
    handleEdit(row) {
      this.$refs.editDialog.edit(row)
    },
    async handleDelete(row) {
      this.$confirm('是否确认删除该数据项？', '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await pageFingerprintBlackListApi.delPageFingerprintBlackList(
            row.fingerprintId
          )
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('删除成功')
          } else {
            this.$xMsgError('删除失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },

    handleOk() {
      this.$refs.table.refresh()
    },
  },
}
</script>
