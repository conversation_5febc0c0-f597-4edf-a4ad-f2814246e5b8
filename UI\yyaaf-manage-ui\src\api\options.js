import request from '@/utils/request'

export function getCounterOrderConfigOptions() {
  return request({
    url: '/counterOrderConfig/options',
    method: 'get',
  })
}

export function getTaskConfigOptions(params) {
  return request({
    url: '/options/getTaskConfigOptions',
    method: 'get',
    params,
  })
}

export function getDeliverTaskPlatform(params) {
  return request({
    url: '/options/getDeliverTaskPlatform',
    method: 'get',
    params,
  })
}

export function getPageCollectRecordStatusTypes() {
  return request({
    url: '/options/getPageCollectRecordStatusTypes',
    method: 'get',
  })
}
