<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="close"
  >
    <el-form ref="form" :model="form" label-width="100px" size="medium">
      <el-row>
        <el-col :span="24">
          <el-form-item label="id" prop="id">
            <el-input :disabled="true" v-model="form.id" placeholder="Id自动生成" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="App名称" prop="appName">
            <el-input v-model="form.appName" placeholder="请输入App名称" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="Url格式" prop="urlFormat">
            <el-input v-model="form.urlFormat" placeholder="请输入URL格式" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="是否启用" prop="enable">
            <x-radio v-model="form.enable" button :options="enableOptions" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </x-dialog>
</template>

<script>
import { deliverTaskPlatformConfigApi } from '@/api'
import { enableOptions } from '@/utils/options'
export default {
  data() {
    return {
      title: '',
      visible: false,
      loading: false,
      form: {
        enable: true,
      },
      enableOptions,
    }
  },
  methods: {
    add() {
      this.reset()
      this.title = '新增'
      this.visible = true
    },
    async edit(row) {
      this.reset()
      this.title = '修改'
      this.visible = true
      this.$xloading.show()
      const res = await deliverTaskPlatformConfigApi.getDetail(row.id)
      if (res.code == 0) {
        this.form = res.data
      }
      this.$xloading.hide()
    },

    close() {
      this.reset()
      this.visible = false
    },

    reset() {
      this.form = {
        enable: true,
      }
      this.$xResetForm('form')
    },

    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          const { id } = data
          if (id != undefined) {
            const res = await deliverTaskPlatformConfigApi.editDeliverTaskPlatformConfig(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await deliverTaskPlatformConfigApi.addDeliverTaskPlatformConfig(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    },
  },
}
</script>

<style></style>
