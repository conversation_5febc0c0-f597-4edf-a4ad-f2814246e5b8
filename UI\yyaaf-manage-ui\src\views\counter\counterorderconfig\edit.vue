<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="close"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px" size="medium">
      <el-row>
        <el-col :span="24">
          <el-form-item label="id" prop="id">
            <el-input :disabled="true" v-model="form.id" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="标题" prop="title">
            <el-input v-model="form.title" placeholder="请输入工单标题" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="工单Url" prop="url">
            <el-input v-model="form.url" placeholder="请输入工单Url" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工单账号" prop="account">
            <el-input v-model="form.account" placeholder="请输入工单账号，非必填" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工单密码" prop="passWord">
            <el-input v-model="form.passWord" placeholder="请输入工单密码" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="UUId" prop="uuId">
            <el-input v-model="form.uuId" :disabled="true" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="SId" prop="sId">
            <el-input v-model="form.sId" :disabled="true" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="WorkId" prop="workId">
            <el-input v-model="form.workId" :disabled="true" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工单号" prop="orderId">
            <el-input v-model="form.orderId" :disabled="true" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工单过期时间" prop="expireTime">
            <el-input v-model="form.expireTime" :disabled="true" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工单状态" prop="state">
            <x-select
              v-model="form.state"
              url="/options/getCounterOrderConfigStateTypes"
            ></x-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="平台" prop="platform">
            <x-radio v-model="form.platform" button :options="platformOptions" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="是否启用" prop="enable">
            <x-radio v-model="form.enable" button :options="enableOptions" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="抓取用户" prop="filterAccounts">
            <el-input
              type="textarea"
              :autosize="{ minRows: 5 }"
              v-model="form.filterAccounts"
              placeholder="请输入需要抓取的用户,不填写全抓，格式：accountId换行accountId2换行accountId3"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </x-dialog>
</template>

<script>
import { enableOptions } from '@/utils/options'
import { counterOrderConfigApi } from '@/api'
export default {
  data() {
    return {
      title: '',
      visible: false,
      loading: false,
      form: {
        enable: true,
        state: 0,
        passWord: '',
      },
      rules: {
        title: [{ required: true, message: '请输入工单标题', trigger: 'blur' }],
        url: [{ required: true, message: '请输入工单链接', trigger: 'blur' }],
        //passWord: [{ required: true, message: '请输入工单密码', trigger: 'blur' }],
        platform: [{ required: true, message: '选择工单平台', trigger: 'blur' }],
      },
      platformOptions: [
        { label: 'IMX', value: 'IMX' },
        { label: 'SCRM', value: 'SCRM' },
        { label: 'SIHAI', value: 'SIHAI' },
        { label: 'WA', value: 'WA' },
      ],
      enableOptions,
    }
  },
  methods: {
    add() {
      this.reset()
      this.title = '新增'
      this.visible = true
    },
    async edit(row) {
      this.reset()
      this.title = '修改'
      this.visible = true
      this.$xloading.show()
      const res = await counterOrderConfigApi.getDetail(row.id)
      if (res.code == 0) {
        this.form = res.data
      }
      this.$xloading.hide()
    },

    close() {
      this.reset()
      this.visible = false
    },

    reset() {
      this.form = {
        enable: true,
        state: 0,
        passWord: '',
      }
      this.$xResetForm('form')
    },

    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          const { id } = data
          if (id != undefined) {
            const res = await counterOrderConfigApi.editCounterOrderConfig(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await counterOrderConfigApi.addCounterOrderConfig(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    },
  },
}
</script>

<style></style>
